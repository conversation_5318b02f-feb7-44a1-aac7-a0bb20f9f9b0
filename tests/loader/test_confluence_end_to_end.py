#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de bout en bout pour le module Confluence dans kbot-load-scheduler.
Ce test vérifie l'intégration complète du module Confluence avec l'architecture du projet.
"""

import os
import sys
import pytest
import json
from unittest.mock import patch, MagicMock
import datetime
from typing import Dict, Any, List

# Ajouter le chemin du projet au PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.dependency.container import Container
from kbotloadscheduler.loader.loader_manager import LoaderManager
from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

# Import des mocks
from tests.testutils.mock_confluence import MockConfluenceAP<PERSON>
from tests.testutils.mock_gcs import mock_gcs_bucket, mock_gcs_blob, MockGCSClient


# Configuration pour les tests
TEST_CONFLUENCE_URL = "https://test-confluence.example.com"
TEST_PERIMETER_CODE = "test-perimeter"
TEST_PAT_TOKEN = "test-pat-token-12345"


@pytest.fixture
def mock_secret_manager():
    """Fixture pour simuler Secret Manager avec les credentials Confluence."""
    with patch("kbotloadscheduler.secret.secret_manager.ConfigWithSecret") as mock:
        config_with_secret = MagicMock()
        
        # Configurer la méthode get_confluence_credentials pour retourner des credentials valides
        config_with_secret.get_confluence_credentials.return_value = {
            "pat_token": TEST_PAT_TOKEN
        }
        
        mock.return_value = config_with_secret
        yield config_with_secret


@pytest.fixture
def mock_env_vars():
    """Fixture pour configurer les variables d'environnement requises."""
    original_env = os.environ.copy()
    
    os.environ["CONFLUENCE_URL"] = TEST_CONFLUENCE_URL
    os.environ["DEFAULT_SPACE_KEY"] = "TEST"
    
    yield
    
    # Restaurer l'environnement original
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def mock_confluence_client():
    """Fixture pour simuler le client Confluence."""
    with patch("kbotloadscheduler.loader.confluence.confluence_loader.ConfluenceClient") as mock_client:
        # Créer une instance du mock Confluence API
        mock_api = MockConfluenceAPI()
        mock_client.return_value = mock_api
        yield mock_api


@pytest.fixture
def mock_gcs():
    """Fixture pour simuler Google Cloud Storage."""
    with patch("google.cloud.storage.Client") as mock_client:
        # Configurer les mocks GCS
        mock_client.return_value = MockGCSClient()
        yield mock_client


@pytest.fixture
def container(mock_secret_manager, mock_env_vars):
    """Fixture pour créer le container de dépendances."""
    container = Container()
    yield container


@pytest.fixture
def test_source():
    """Fixture pour créer une source de test."""
    source_config = {
        "spaces": ["TEST", "DOCS"],
        "max_results": 10,
        "include_attachments": True,
        "content_types": ["page"],
        "labels": ["test-label"]
    }
    
    source = SourceBean(
        id=1,
        code="test_confluence",
        label="Test Confluence",
        src_type="confluence",
        configuration=json.dumps(source_config),
        domain_code="test-domain",
        perimeter_code=TEST_PERIMETER_CODE
    )
    
    yield source


def setup_mock_confluence_data(mock_api: MockConfluenceAPI):
    """Configure les données simulées pour l'API Confluence."""
    # Créer quelques pages Confluence simulées
    pages = [
        {
            "id": f"page{i}",
            "title": f"Test Page {i}",
            "type": "page",
            "space": {"key": "TEST", "name": "Test Space"},
            "version": {"number": 1, "when": datetime.datetime.now().isoformat()},
            "body": {"storage": {"value": f"<p>Test content {i}</p>"}},
            "_links": {"webui": f"/pages/viewpage.action?pageId=page{i}"}
        }
        for i in range(1, 4)
    ]
    
    # Ajouter des pièces jointes à la première page
    attachments = [
        {
            "id": f"att{i}",
            "title": f"attachment{i}.pdf",
            "extensions": {"fileSize": 1024, "mediaType": "application/pdf"},
            "version": {"createdDate": datetime.datetime.now().isoformat()},
            "_links": {"download": f"/download/attachments/page1/attachment{i}.pdf"}
        }
        for i in range(1, 3)
    ]
    
    # Configurer le mock pour renvoyer ces données
    mock_api.set_mock_pages(pages)
    mock_api.set_mock_attachments("page1", attachments)
    mock_api.set_mock_attachment_content(b"test attachment content")


class TestConfluenceEndToEnd:
    """Tests de bout en bout pour le module Confluence."""
    
    def test_get_document_list(self, container, test_source, mock_confluence_client, mock_env_vars):
        """Teste la récupération de la liste des documents Confluence."""
        # Configurer les données simulées
        setup_mock_confluence_data(mock_confluence_client)
        
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        assert confluence_loader is not None, "Le loader Confluence n'a pas été trouvé"
        
        # Obtenir la liste des documents
        documents = confluence_loader.get_document_list(test_source)
        
        # Vérifications
        assert len(documents) > 0, "Aucun document n'a été récupéré"
        
        # Vérifier le format des documents
        for doc in documents:
            assert isinstance(doc, DocumentBean), "Le document n'est pas une instance de DocumentBean"
            assert doc.id.startswith(f"{test_source.domain_code}/{test_source.code}/"), \
                "L'ID du document ne suit pas le format attendu"
            assert doc.name is not None and doc.name != "", "Le nom du document est vide"
            assert doc.path is not None and doc.path != "", "Le chemin du document est vide"
            assert doc.modification_time is not None, "La date de modification est manquante"
    
    def test_get_document(self, container, test_source, mock_confluence_client, mock_gcs, mock_env_vars):
        """Teste la récupération d'un document spécifique."""
        # Configurer les données simulées
        setup_mock_confluence_data(mock_confluence_client)
        
        # Créer un DocumentBean pour le test
        document = DocumentBean(
            id=f"{test_source.domain_code}/{test_source.code}/page_page1",
            name="Test Page 1",
            path="/pages/viewpage.action?pageId=page1",
            modification_time=datetime.datetime.now().isoformat()
        )
        
        # Chemin de sortie GCS
        output_path = "test-bucket/confluence-test/page1"
        
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        
        # Patcher la méthode run() de SyncOrchestrator pour simuler une synchronisation réussie
        with patch("kbotloadscheduler.loader.confluence.orchestrator.SyncOrchestrator.run") as mock_run:
            mock_run.return_value = {
                "total_content_items": 1,
                "stored_content_items": 1,
                "processing_time_seconds": 0.5
            }
            
            # Récupérer le document
            metadata = confluence_loader.get_document(test_source, document, output_path)
            
            # Vérifications
            assert metadata is not None, "Aucune métadonnée n'a été retournée"
            assert "document_id" in metadata, "document_id manquant dans les métadonnées"
            assert metadata["document_id"] == document.id, "L'ID du document ne correspond pas"
            assert "location" in metadata, "location manquant dans les métadonnées"
            assert metadata["location"] == output_path, "Le chemin de sortie ne correspond pas"
            assert "source_type" in metadata and metadata["source_type"] == "confluence", \
                "Le type de source ne correspond pas"
            assert "confluence_sync_stats" in metadata, "Statistiques de synchronisation manquantes"
    
    def test_loader_manager_integration(self, container, mock_env_vars):
        """Teste que le LoaderManager est correctement configuré avec le loader Confluence."""
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()
        
        # Vérifier que le LoaderManager contient bien le loader Confluence
        loader_types = loader_manager.get_loader_types()
        assert "confluence" in loader_types, "Le type 'confluence' n'est pas enregistré dans le LoaderManager"
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        assert isinstance(confluence_loader, ConfluenceLoader), \
            "Le loader renvoyé n'est pas une instance de ConfluenceLoader"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
